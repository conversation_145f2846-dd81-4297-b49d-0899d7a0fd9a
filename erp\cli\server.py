"""
Server management commands for ERP CLI
"""

import argparse
import asyncio
from typing import Optional

from ..config import config
from ..logging import get_logger
from .base import BaseCommand, CommandGroup


class StartCommand(BaseCommand):
    """Start HTTP server command"""

    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument(
            "--db",
            type=str,
            dest="db_name",
            help="Database name to start with (optional)",
        )
        parser.add_argument(
            "--host", type=str, help="Host interface to bind to (overrides config)"
        )
        parser.add_argument(
            "--port", type=int, help="Port to bind to (overrides config)"
        )
        parser.add_argument(
            "--reload",
            action="store_true",
            help="Enable auto-reload for development (watches Python files)",
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle server start command"""

        async def _start_server():
            if args.db_name:
                # Start with specific database
                await self._start_with_database(
                    args.db_name, args.host, args.port, args.reload
                )
                return

            # Start without database context (multi-database mode)
            await self._start_without_database(
                args.host, args.port, args.reload
            )

        # Run the async function - let exceptions bubble up to main()
        asyncio.run(_start_server())
        return 0

    async def _start_with_database(
        self,
        db_name: str,
        host: Optional[str] = None,
        port: Optional[int] = None,
        reload: bool = False,
    ):
        """Start HTTP server with specific database"""
        # Check if database exists and is initialized
        if not await self._is_database_initialized(db_name):
            self.print_error(
                f"Database '{db_name}' is not initialized with ERP system"
            )
            self.print_error(
                f"Please run 'erp-bin init {db_name}' first to initialize the database"
            )
            raise RuntimeError(f"Database '{db_name}' is not initialized")

        self.print_success(f"Database '{db_name}' is initialized and ready")

        # Start the HTTP server
        await self._start_http_server(db_name, host, port, reload)

    async def _start_without_database(
        self,
        host: Optional[str] = None,
        port: Optional[int] = None,
        reload: bool = False,
    ):
        """Start HTTP server without database context (multi-database mode)"""
        self.print_info("Starting HTTP server without database context...")

        # Get server configuration from config or use provided values
        server_host = host or config.get(
            "options", "http_interface", fallback="127.0.0.1"
        )
        server_port = port or config.getint("options", "http_port", fallback=8069)

        self.print_info(f"Server starting on http://{server_host}:{server_port}")
        self.print_info("Multi-database mode - no default database")
        if reload:
            self.print_info(
                "Auto-reload enabled - watching Python files for changes"
            )
        self.print_success("Ready - Press Ctrl+C to stop")

        # Don't set any database in config when starting without context
        # This allows the server to run in multi-database mode

        # Create and start the server
        await self._create_and_start_server(server_host, server_port, reload)

    async def _start_http_server(
        self,
        db_name: str,
        host: Optional[str] = None,
        port: Optional[int] = None,
        reload: bool = False,
    ):
        """Start the FastAPI HTTP server with the initialized database"""
        self.print_info(f"Starting HTTP server with database '{db_name}'...")

        # Get server configuration from config or use provided values
        server_host = host or config.get(
            "options", "http_interface", fallback="127.0.0.1"
        )
        server_port = port or config.getint("options", "http_port", fallback=8069)

        self.print_info(f"Server starting on http://{server_host}:{server_port}")
        self.print_info(f"Database: {db_name}")
        if reload:
            self.print_info(
                "Auto-reload enabled - watching Python files for changes"
            )
        self.print_success("Ready - Press Ctrl+C to stop")

        # Set the database as the current database in config
        config.set("options", "db_name", db_name)

        # Create and start the server
        await self._create_and_start_server(server_host, server_port, reload)

    async def _create_and_start_server(
        self, host: str, port: int, reload: bool = False
    ):
        """Create and start the uvicorn server"""
        import uvicorn

        from ..server import create_app

        # Create the FastAPI app
        app = create_app()

        # Configure uvicorn
        uvicorn_config = uvicorn.Config(
            app=app,
            host=host,
            port=port,
            log_level="info",
            access_log=True,
            reload=reload,
            reload_includes=["*.py"] if reload else None,
            reload_excludes=(
                ["*.pyc", "__pycache__/*", "*.log", "*.tmp"] if reload else None
            ),
        )

        # Start the server
        server = uvicorn.Server(uvicorn_config)
        await server.serve()

    async def _is_database_initialized(self, db_name: str) -> bool:
        """Check if a database is already initialized with ERP system"""
        try:
            from ..database.memory.registry_manager import MemoryRegistryManager

            return await MemoryRegistryManager._is_base_module_installed(db_name)
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"Error checking database initialization status: {e}")
            return False


class ServerCommandGroup(CommandGroup):
    """Server command group"""

    def __init__(self):
        super().__init__()
        self.register_command(StartCommand())

    def add_commands(self, subparsers, parent_parser=None):
        """Add server commands to subparsers"""
        # Start server command
        start_parser = subparsers.add_parser(
            "start",
            help="Start HTTP server",
            parents=[parent_parser] if parent_parser else [],
        )
        self.commands["start"].add_arguments(start_parser)
