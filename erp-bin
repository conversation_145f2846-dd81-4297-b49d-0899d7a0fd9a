#!/usr/bin/env python3
"""
ERP System - Command Line Interface
Modular CLI tool for database and server management
"""
import sys
from pathlib import Path

# Add current directory to Python path
erp_root = Path(__file__).parent
sys.path.insert(0, str(erp_root))

# Import the modular CLI system
from erp.cli import ERPCLIManager


def main():
    """Main entry point for the ERP CLI - centralized error handling"""
    import traceback

    try:
        # Create and run the CLI manager
        manager = ERPCLIManager()
        return manager.run()
    except KeyboardInterrupt:
        print("\n🛑 Operation cancelled by user")
        return 130
    except SystemExit as e:
        # Handle explicit sys.exit() calls
        return e.code if e.code is not None else 0
    except Exception as e:
        # Centralized error handling for all unhandled exceptions
        print(f"💥 Unexpected error: {e}")
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
