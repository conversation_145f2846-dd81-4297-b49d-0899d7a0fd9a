"""
Base addon hooks

Standard addon installation hooks for the base addon.
The base addon now uses the same installation process as regular addons.
"""

from erp.addons.hooks import post_install_hook
from erp.logging import get_logger

logger = get_logger(__name__)


@post_install_hook('base', priority=10)
async def base_post_install(context):
    """
    Post-installation hook for base addon.

    This hook runs after the base addon installation and ensures
    that core functionality is properly initialized.
    """
    logger.info("Base addon post-install hook executed")

    # The core IR population and schema sync is handled automatically
    # by the core_hooks.py post-install hook that runs for all addons

    return True
