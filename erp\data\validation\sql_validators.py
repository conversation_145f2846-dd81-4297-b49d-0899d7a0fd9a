"""
Enhanced SQL-based field validation for XML data loading

This module provides comprehensive field validation using raw SQL queries
without any dependency on AppRegistry or model registry.
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from ...database.connection.manager import DatabaseManager
from ...logging import get_logger
from ..sql_helpers import ModelSQLHelpers, SQLHelpers, XMLIDSQLHelpers


class SQLFieldValidator:
    """
    SQL-based field validator that uses raw database queries
    for validation without AppRegistry dependency.
    """

    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize SQL field validator.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_sql = XMLIDSQLHelpers(self.sql)
        self.logger = get_logger(__name__)

    async def validate_boolean_field(self, value: Any) -> Dict[str, Any]:
        """
        Validate and convert boolean field value.

        Args:
            value: Value to validate

        Returns:
            Dictionary with validation result and converted value
        """
        result = {"valid": False, "value": None, "error": None}

        if value is None:
            result["valid"] = True
            result["value"] = None
            return result

        # Handle boolean values
        if isinstance(value, bool):
            result["valid"] = True
            result["value"] = value
            return result

        # Handle string values
        if isinstance(value, str):
            value_lower = value.lower().strip()
            if value_lower in ("true", "1", "yes", "on", "t"):
                result["valid"] = True
                result["value"] = True
                return result
            elif value_lower in ("false", "0", "no", "off", "f", ""):
                result["valid"] = True
                result["value"] = False
                return result

        # Handle numeric values
        if isinstance(value, (int, float)):
            result["valid"] = True
            result["value"] = bool(value)
            return result

        result["error"] = f"Cannot convert '{value}' to boolean"
        return result

    async def validate_reference_field(
        self, value: Any, selection: List[tuple] = None
    ) -> Dict[str, Any]:
        """
        Validate reference field value using raw SQL.

        Args:
            value: Reference value in format "model,id"
            selection: Optional list of allowed models

        Returns:
            Dictionary with validation result
        """
        result = {"valid": False, "value": None, "error": None}

        if value is None:
            result["valid"] = True
            result["value"] = None
            return result

        if not isinstance(value, str):
            result["error"] = "Reference value must be a string"
            return result

        if "," not in value:
            result["error"] = "Reference value must be in format 'model,id'"
            return result

        try:
            model_name, record_id = value.split(",", 1)
            model_name = model_name.strip()
            record_id = record_id.strip()

            # Validate model is in selection if provided
            if selection:
                valid_models = [item[0] for item in selection if len(item) >= 2]
                if model_name not in valid_models:
                    result["error"] = f"Model '{model_name}' not in allowed selection"
                    return result

            # Validate model exists
            if not await self.sql.validate_model_exists(model_name):
                result["error"] = f"Model '{model_name}' does not exist"
                return result

            # Validate record exists
            if not await self.model_sql.validate_record_exists(model_name, record_id):
                result["error"] = f"Record {record_id} does not exist in {model_name}"
                return result

            result["valid"] = True
            result["value"] = value
            return result

        except Exception as e:
            result["error"] = f"Error validating reference: {e}"
            return result

    async def validate_many2one_field(
        self, value: Any, comodel_name: str
    ) -> Dict[str, Any]:
        """
        Validate Many2one field value using raw SQL.

        Args:
            value: Record ID or XML ID reference
            comodel_name: Target model name

        Returns:
            Dictionary with validation result and resolved ID
        """
        result = {"valid": False, "value": None, "error": None}

        if value is None:
            result["valid"] = True
            result["value"] = None
            return result

        # Handle XML ID reference
        if isinstance(value, str) and not value.isdigit():
            xmlid_result = await self.xmlid_sql.validate_xmlid_reference(
                value, comodel_name
            )
            if xmlid_result["valid"]:
                result["valid"] = True
                result["value"] = xmlid_result["res_id"]
                return result
            else:
                result["error"] = xmlid_result["error"]
                return result

        # Handle direct ID
        try:
            record_id = str(value)
            if await self.model_sql.validate_record_exists(comodel_name, record_id):
                result["valid"] = True
                result["value"] = record_id
                return result
            else:
                result["error"] = f"Record {record_id} does not exist in {comodel_name}"
                return result
        except Exception as e:
            result["error"] = f"Error validating Many2one field: {e}"
            return result

    async def validate_selection_field(
        self, value: Any, selection: List[tuple]
    ) -> Dict[str, Any]:
        """
        Validate selection field value.

        Args:
            value: Value to validate
            selection: List of (key, label) tuples

        Returns:
            Dictionary with validation result
        """
        result = {"valid": False, "value": None, "error": None}

        if value is None:
            result["valid"] = True
            result["value"] = None
            return result

        # Get valid values from selection
        valid_values = [item[0] for item in selection if len(item) >= 2]

        if value in valid_values:
            result["valid"] = True
            result["value"] = value
            return result

        result["error"] = f"Value '{value}' not in selection {valid_values}"
        return result

    async def validate_char_field(
        self, value: Any, size: int = None, required: bool = False
    ) -> Dict[str, Any]:
        """
        Validate character field value.

        Args:
            value: Value to validate
            size: Maximum length
            required: Whether field is required

        Returns:
            Dictionary with validation result
        """
        result = {"valid": False, "value": None, "error": None}

        if value is None:
            if required:
                result["error"] = "Field is required"
                return result
            result["valid"] = True
            result["value"] = None
            return result

        # Convert to string
        try:
            str_value = str(value).strip() if value is not None else ""
        except (ValueError, TypeError):
            result["error"] = f"Cannot convert {type(value).__name__} to string"
            return result

        # Check required
        if required and not str_value:
            result["error"] = "Field is required"
            return result

        # Check size
        if size and len(str_value) > size:
            result["error"] = f"String too long: {len(str_value)} > {size}"
            return result

        result["valid"] = True
        result["value"] = str_value
        return result

    async def validate_integer_field(
        self, value: Any, required: bool = False
    ) -> Dict[str, Any]:
        """
        Validate integer field value.

        Args:
            value: Value to validate
            required: Whether field is required

        Returns:
            Dictionary with validation result
        """
        result = {"valid": False, "value": None, "error": None}

        if value is None:
            if required:
                result["error"] = "Field is required"
                return result
            result["valid"] = True
            result["value"] = None
            return result

        # Convert to integer
        try:
            int_value = int(value)
            result["valid"] = True
            result["value"] = int_value
            return result
        except (ValueError, TypeError):
            result["error"] = f"Cannot convert '{value}' to integer"
            return result

    async def validate_float_field(
        self, value: Any, required: bool = False
    ) -> Dict[str, Any]:
        """
        Validate float field value.

        Args:
            value: Value to validate
            required: Whether field is required

        Returns:
            Dictionary with validation result
        """
        result = {"valid": False, "value": None, "error": None}

        if value is None:
            if required:
                result["error"] = "Field is required"
                return result
            result["valid"] = True
            result["value"] = None
            return result

        # Convert to float
        try:
            float_value = float(value)
            result["valid"] = True
            result["value"] = float_value
            return result
        except (ValueError, TypeError):
            result["error"] = f"Cannot convert '{value}' to float"
            return result

    async def validate_field_by_type(
        self, field_type: str, value: Any, field_info: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Validate field value based on field type using raw SQL.

        Args:
            field_type: Field type (boolean, char, integer, etc.)
            value: Value to validate
            field_info: Additional field information

        Returns:
            Dictionary with validation result
        """
        field_info = field_info or {}

        # Normalize field type to lowercase for comparison
        field_type_lower = field_type.lower() if field_type else ""

        if field_type_lower == "boolean":
            return await self.validate_boolean_field(value)
        elif field_type_lower == "char":
            return await self.validate_char_field(
                value,
                size=field_info.get("size"),
                required=field_info.get("required", False),
            )
        elif field_type_lower == "integer":
            return await self.validate_integer_field(
                value, required=field_info.get("required", False)
            )
        elif field_type_lower == "float":
            return await self.validate_float_field(
                value, required=field_info.get("required", False)
            )
        elif field_type_lower == "selection":
            return await self.validate_selection_field(
                value, field_info.get("selection", [])
            )
        elif field_type_lower == "many2one":
            return await self.validate_many2one_field(
                value, field_info.get("relation", "")
            )
        elif field_type_lower == "reference":
            return await self.validate_reference_field(
                value, field_info.get("selection", [])
            )
        else:
            # Default validation for unknown types
            return {"valid": True, "value": value, "error": None}
