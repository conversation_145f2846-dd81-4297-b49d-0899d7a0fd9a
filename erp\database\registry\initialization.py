"""
Database initialization logic
"""

from ...logging import get_logger
from .database_registry import DatabaseRegistry


class DatabaseInitializer:
    """Database initialization with addon system"""

    _logger = get_logger(__name__)

    @classmethod
    async def initialize_database(cls, db_name: str) -> None:
        """Initialize database with required tables and data"""
        from ...addons import AddonManager
        from ...environment import EnvironmentManager

        try:
            # Ensure database connection exists
            await DatabaseRegistry.get_database(db_name)

            # Set as current database early for addon installation
            DatabaseRegistry.set_current_database(db_name)

            # Create environment for addon installation
            env = await EnvironmentManager.create_environment(
                db_name, 1
            )  # Use admin user

            # Create addon manager and install base addon
            manager = AddonManager()
            await manager.discover_addons()

            # Install base addon within a transaction context
            # This ensures proper transaction management for CLI operations
            async with EnvironmentManager.transaction(env):
                await manager.install_addon("base", force=True, env=env)
                cls._logger.info("✓ Base addon installed successfully")

                # Important: Update the registry WITHIN the transaction context
                # This ensures the base module is fully committed before registry update
                cls._logger.debug("Updating registry after base module installation...")
                from ...database.memory.registry_manager import MemoryRegistryManager

                # Update the registry - special handling for base module is in MemoryRegistryManager
                await MemoryRegistryManager.update_registry_after_module_action(
                    db_name, "base", "install"
                )
                cls._logger.debug(
                    "✅ Registry successfully updated for database '%s'", db_name
                )
                cls._logger.info("Database %s initialized successfully", db_name)

        except Exception as e:
            cls._logger.error(f"Error initializing database {db_name}: {e}")
            # Rollback database creation on any error
            from .lifecycle import DatabaseLifecycle

            try:
                await DatabaseLifecycle.rollback_database_creation(db_name)
            except Exception as rollback_error:
                cls._logger.error(
                    "Failed to rollback database creation: %s", rollback_error
                )

            # Re-raise the original exception to ensure fail-fast behavior
            raise
